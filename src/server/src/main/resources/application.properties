# Blood on the Clocktower Assistant Configuration

# Server Configuration
server.port=8080

# Static Resources Configuration
spring.web.resources.static-locations=file:/app/static/,classpath:/static/
spring.web.resources.cache.period=********
spring.mvc.static-path-pattern=/**
# Disable welcome page to avoid conflicts
spring.mvc.view.prefix=
spring.mvc.view.suffix=

# CORS Configuration (handled in code for more control)

# Firebase Configuration
firebase.service-account-path=${FIREBASE_SERVICE_ACCOUNT_PATH:/app/secrets/firebase-service-account.json}
firebase.project-id=${FIREBASE_PROJECT_ID:}

# Actuator Configuration
management.endpoints.web.exposure.include=health,info
management.endpoint.health.show-details=when-authorized

# Production Profile Configuration
---
spring.config.activate.on-profile=production
logging.level.org.springframework=INFO
logging.level.com.botc.assistant=DEBUG
