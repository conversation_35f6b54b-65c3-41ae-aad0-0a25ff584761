package com.botc.assistant.config;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.google.firebase.auth.FirebaseAuth;
import com.google.cloud.firestore.Firestore;
import com.google.firebase.cloud.FirestoreClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;

import javax.annotation.PostConstruct;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * Firebase configuration for the Blood on the Clocktower Assistant.
 * Initializes Firebase Admin SDK with service account credentials.
 */
@Configuration
public class FirebaseConfig {

    private static final Logger logger = LoggerFactory.getLogger(FirebaseConfig.class);

    @Value("${firebase.service-account-path:/app/secrets/firebase-service-account.json}")
    private String serviceAccountPath;

    @Value("${firebase.project-id:}")
    private String projectId;

    /**
     * Initialize Firebase App with service account credentials.
     * This method runs after the bean is constructed and dependencies are injected.
     */
    @PostConstruct
    public void initializeFirebase() {
        try {
            // Check if Firebase is already initialized
            if (!FirebaseApp.getApps().isEmpty()) {
                logger.info("Firebase already initialized");
                return;
            }

            GoogleCredentials credentials = getCredentials();

            FirebaseOptions.Builder optionsBuilder = FirebaseOptions.builder()
                    .setCredentials(credentials);

            // Set project ID if provided
            if (projectId != null && !projectId.trim().isEmpty()) {
                optionsBuilder.setProjectId(projectId);
                logger.info("Using configured project ID: {}", projectId);
            }

            FirebaseOptions options = optionsBuilder.build();

            FirebaseApp.initializeApp(options);
            logger.info("Firebase initialized successfully");

        } catch (Exception e) {
            logger.error("Failed to initialize Firebase: {}", e.getMessage(), e);
            // Don't throw exception to allow application to start without Firebase
            // This allows for graceful degradation
        }
    }

    /**
     * Get Google credentials from service account file.
     * Tries multiple locations: file system, then classpath.
     */
    private GoogleCredentials getCredentials() throws IOException {
        InputStream serviceAccount = null;

        try {
            // Try file system first (for Docker deployment)
            Resource fileResource = new FileSystemResource(serviceAccountPath);
            if (fileResource.exists()) {
                logger.info("Loading Firebase credentials from file system: {}", serviceAccountPath);
                serviceAccount = new FileInputStream(serviceAccountPath);
            } else {
                // Fallback to classpath (for development)
                Resource classpathResource = new ClassPathResource("firebase-service-account.json");
                if (classpathResource.exists()) {
                    logger.info("Loading Firebase credentials from classpath");
                    serviceAccount = classpathResource.getInputStream();
                } else {
                    throw new IOException("Firebase service account file not found at: " + serviceAccountPath +
                            " or in classpath");
                }
            }

            return GoogleCredentials.fromStream(serviceAccount);

        } finally {
            if (serviceAccount != null) {
                serviceAccount.close();
            }
        }
    }

    /**
     * Provides Firebase Auth instance.
     * Returns null if Firebase is not initialized.
     */
    @Bean
    public FirebaseAuth firebaseAuth() {
        try {
            if (FirebaseApp.getApps().isEmpty()) {
                logger.warn("Firebase not initialized, FirebaseAuth bean will be null");
                return null;
            }
            return FirebaseAuth.getInstance();
        } catch (Exception e) {
            logger.error("Failed to get FirebaseAuth instance: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Provides Firestore instance.
     * Returns null if Firebase is not initialized.
     */
    @Bean
    public Firestore firestore() {
        try {
            if (FirebaseApp.getApps().isEmpty()) {
                logger.warn("Firebase not initialized, Firestore bean will be null");
                return null;
            }
            return FirestoreClient.getFirestore();
        } catch (Exception e) {
            logger.error("Failed to get Firestore instance: {}", e.getMessage());
            return null;
        }
    }
}
