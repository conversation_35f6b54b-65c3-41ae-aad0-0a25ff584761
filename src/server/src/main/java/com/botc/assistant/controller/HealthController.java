package com.botc.assistant.controller;

import com.google.firebase.FirebaseApp;
import com.google.firebase.auth.FirebaseAuth;
import com.google.cloud.firestore.Firestore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Health check controller for monitoring application status.
 * Provides endpoints to check Firebase connectivity and overall health.
 */
@RestController
@RequestMapping("/api/health")
public class HealthController {

    private static final Logger logger = LoggerFactory.getLogger(HealthController.class);

    @Autowired(required = false)
    private FirebaseAuth firebaseAuth;

    @Autowired(required = false)
    private Firestore firestore;

    /**
     * Basic health check endpoint.
     * Returns application status and Firebase connectivity.
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();

        try {
            health.put("status", "UP");
            health.put("timestamp", System.currentTimeMillis());

            // Check Firebase initialization
            boolean firebaseInitialized = !FirebaseApp.getApps().isEmpty();
            health.put("firebase", Map.of(
                    "initialized", firebaseInitialized,
                    "auth", firebaseAuth != null,
                    "firestore", firestore != null));

            // If Firebase is not initialized, still return 200 but with warning
            if (!firebaseInitialized) {
                health.put("warning", "Firebase not initialized - check service account configuration");
                logger.warn("Health check: Firebase not initialized");
            }

            return ResponseEntity.ok(health);

        } catch (Exception e) {
            logger.error("Health check failed: {}", e.getMessage(), e);
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            return ResponseEntity.status(503).body(health);
        }
    }

    /**
     * Firebase-specific health check.
     * Returns detailed Firebase service status.
     */
    @GetMapping("/firebase")
    public ResponseEntity<Map<String, Object>> firebaseHealth() {
        Map<String, Object> status = new HashMap<>();

        try {
            boolean firebaseInitialized = !FirebaseApp.getApps().isEmpty();
            status.put("initialized", firebaseInitialized);

            if (firebaseInitialized) {
                FirebaseApp app = FirebaseApp.getInstance();
                status.put("appName", app.getName());
                status.put("projectId", app.getOptions().getProjectId());

                // Test Firebase Auth
                status.put("auth", Map.of(
                        "available", firebaseAuth != null,
                        "status", firebaseAuth != null ? "connected" : "unavailable"));

                // Test Firestore
                status.put("firestore", Map.of(
                        "available", firestore != null,
                        "status", firestore != null ? "connected" : "unavailable"));

                status.put("overall", "healthy");
                return ResponseEntity.ok(status);

            } else {
                status.put("overall", "not_initialized");
                status.put("message", "Firebase not initialized - check service account file and configuration");
                return ResponseEntity.status(503).body(status);
            }

        } catch (Exception e) {
            logger.error("Firebase health check failed: {}", e.getMessage(), e);
            status.put("overall", "error");
            status.put("error", e.getMessage());
            return ResponseEntity.status(503).body(status);
        }
    }
}
