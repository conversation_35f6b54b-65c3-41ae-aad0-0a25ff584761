// Component to display authentication status and user info
import { useAuth } from '../hooks/useAuthContext';
import { logoutUser } from '../services/authService';

export default function AuthStatus() {
    const { isAuthenticated, userProfile, displayName, isNarrator, loading } = useAuth();

    const handleLogout = async () => {
        try {
            await logoutUser();
        } catch (error) {
            console.error('Logout error:', error);
        }
    };

    if (loading) {
        return <div>Chargement...</div>;
    }

    if (!isAuthenticated) {
        return (
            <div className="auth-status">
                <p>Non connecté</p>
            </div>
        );
    }

    return (
        <div className="auth-status">
            <div className="user-info">
                <h3>Bienvenue, {displayName}!</h3>
                {userProfile && (
                    <div className="user-details">
                        <p><strong>Nom:</strong> {userProfile.prenom} {userProfile.nom}</p>
                        <p><strong>Email:</strong> {userProfile.email}</p>
                        <p><strong>Pseudo:</strong> {userProfile.pseudo}</p>
                        <p><strong>Statut:</strong> {isNarrator ? 'Narrateur' : 'Joueur'}</p>
                    </div>
                )}
            </div>
            <button onClick={handleLogout} className="logout-button">
                Se déconnecter
            </button>
        </div>
    );
}
