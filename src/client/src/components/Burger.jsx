import { useState } from 'react';
import { Link, useNavigate } from "react-router-dom";
import MenuIcon from '@mui/icons-material/Menu';
import { useAuth } from '../hooks/useAuthContext';
import { useToast } from '../hooks/useToastContext';
import { logoutUser } from '../services/authService';

export default function MenuBurger({ status, mj }) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const login = status;
  const isMJ = mj;
  const navigate = useNavigate();
  const { displayName } = useAuth();
  const { showToast } = useToast();

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      await logoutUser();
      setIsOpen(false); // Close menu after logout
      navigate('/'); // Redirect to home page
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  const handlePlayClick = () => {
    setIsOpen(false); // Close menu

    // Role-based routing with placeholder behavior
    if (isMJ) {
      // Storyteller: redirect to create game
      navigate('/'); // Temporary redirect to home
      showToast('Creating games feature in construction', 'info', 4000);
    } else {
      // Player: redirect to join game
      navigate('/'); // Temporary redirect to home
      showToast('Joining games feature in construction', 'info', 4000);
    }
  };

  return (
    <nav className={`menu-burger ${isOpen ? 'isOpen' : ''}`}>
      <button className="burger-button" onClick={() => setIsOpen(!isOpen)}>
        <MenuIcon sx={{ color: "rgb(255, 251, 157)", fontSize: 60 }} />
      </button>

      <div className="menu-links">
        {/* User status indicator - only show when authenticated */}
        {login && (
          <div className="user-status-indicator">
            <span className="user-badge">👤 {displayName}</span>
          </div>
        )}

        {/* Authentication-aware login/logout button */}
        {!login ? (
          <Link to="/login" className="menu-link" id="login-link">
            Se connecter
          </Link>
        ) : (
          <button
            onClick={handleLogout}
            className="menu-link logout-button-nav"
            disabled={isLoggingOut}
          >
            {isLoggingOut ? 'Déconnexion...' : 'Se déconnecter'}
          </button>
        )}

        {/* Play button - only show when authenticated */}
        {login && (
          <button onClick={handlePlayClick} className="menu-link play-button">
            Jouer
          </button>
        )}

        {/* Static navigation links */}
        <Link to="/rules" className="menu-link">Règles</Link>
        <Link to="/roles" className="menu-link">Rôles</Link>

        {/* Role-specific links - only show when authenticated */}
        {login ? (
          isMJ ? (
            <>
              <Link to="/tablemj" className="menu-link">Table MJ</Link>
              <Link to="/tour" className="menu-link">Tour</Link>
            </>
          ) : (
            <>
              <Link to="/table" className="menu-link">Table</Link>
              <Link to="/partie" className="menu-link">Partie</Link>
            </>
          )
        ) : null}
      </div>
    </nav>
  );
}
