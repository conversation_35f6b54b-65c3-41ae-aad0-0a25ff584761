# 🔐 Security Guide: Environment Variables and Secrets

This guide explains how to safely pass environment variables and sensitive files (like Firebase service account JSON) to your Docker container.

## 📋 Overview

Your application needs:
1. **Client-side Firebase config** (public, but environment-specific)
2. **Server-side Firebase service account** (private, sensitive)
3. **Other environment variables** (database URLs, API keys, etc.)

## 🚀 Quick Setup

### 1. Create Environment Files

#### Production Environment File
Create `/opt/botc-assistant/.env` on your server:

```bash
# Server Configuration
SPRING_PROFILES_ACTIVE=production
SERVER_PORT=8080

# Firebase Admin SDK Configuration
FIREBASE_SERVICE_ACCOUNT_PATH=/app/secrets/firebase-service-account.json

# Database Configuration (if needed)
SPRING_DATASOURCE_URL=*****************************

# Logging Configuration
LOGGING_LEVEL_ORG_SPRINGFRAMEWORK=INFO
LOGGING_LEVEL_COM_BOTC_ASSISTANT=DEBUG

# CORS Configuration (if needed)
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Health Check Configuration
MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE=health,info
MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS=when-authorized
```

#### Client Environment File
Create `src/client/.env.production`:

```bash
# Firebase Client Configuration (Public - Safe to include in build)
VITE_FIREBASE_API_KEY=your-firebase-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
VITE_FIREBASE_APP_ID=your-app-id
VITE_FIREBASE_MEASUREMENT_ID=your-measurement-id
```

### 2. Secure Firebase Service Account

#### Copy the Service Account File
```bash
# On your server, create the secrets directory
sudo mkdir -p /opt/botc-assistant/secrets
sudo chmod 700 /opt/botc-assistant/secrets

# Copy your Firebase service account JSON file
sudo cp firebase-service-account.json /opt/botc-assistant/secrets/
sudo chmod 600 /opt/botc-assistant/secrets/firebase-service-account.json
sudo chown botcassistant:botcassistant /opt/botc-assistant/secrets/firebase-service-account.json
```

### 3. Deploy with Secrets

The updated deployment script automatically:
- Loads environment variables from `/opt/botc-assistant/.env`
- Mounts the Firebase service account file as read-only
- Sets proper permissions and security

```bash
# Deploy your application
./deploy/deploy.sh your-docker-image:tag
```

## 🔒 Security Best Practices

### File Permissions
```bash
# Environment file - readable by app user only
chmod 600 /opt/botc-assistant/.env
chown botcassistant:botcassistant /opt/botc-assistant/.env

# Secrets directory - accessible by app user only
chmod 700 /opt/botc-assistant/secrets
chown botcassistant:botcassistant /opt/botc-assistant/secrets

# Service account file - read-only for app user
chmod 600 /opt/botc-assistant/secrets/firebase-service-account.json
chown botcassistant:botcassistant /opt/botc-assistant/secrets/firebase-service-account.json
```

### Docker Security
- Secrets are mounted as read-only volumes
- Non-root user inside container
- Environment files are not copied into image layers
- Secrets directory has restricted access (700)

## 🛠️ Development vs Production

### Development
- Use `src/client/.env.local` for local Firebase config
- Service account file can be in `src/server/src/main/resources/` (gitignored)
- Environment variables can be set in IDE or command line

### Production
- Environment file: `/opt/botc-assistant/.env`
- Service account: `/opt/botc-assistant/secrets/firebase-service-account.json`
- Mounted as volumes, not copied into image

## 🚨 What NOT to Do

❌ **Never do these:**
- Don't put secrets in Dockerfile
- Don't commit `.env` files with real values
- Don't use `COPY` for secret files in Dockerfile
- Don't use environment variables for large JSON files
- Don't give 777 permissions to secret files

✅ **Always do these:**
- Use volume mounts for secrets
- Set restrictive file permissions (600/700)
- Use environment files for configuration
- Keep secrets out of image layers
- Use non-root users in containers

## 🔧 Troubleshooting

### Check File Permissions
```bash
ls -la /opt/botc-assistant/.env
ls -la /opt/botc-assistant/secrets/
```

### Verify Container Mounts
```bash
docker exec botc-assistant-container ls -la /app/secrets/
docker exec botc-assistant-container cat /app/secrets/firebase-service-account.json
```

### Check Environment Variables
```bash
docker exec botc-assistant-container env | grep FIREBASE
```

## 📚 Additional Resources

- [Docker Secrets Documentation](https://docs.docker.com/engine/swarm/secrets/)
- [Spring Boot External Configuration](https://docs.spring.io/spring-boot/docs/current/reference/html/features.html#features.external-config)
- [Firebase Admin SDK Setup](https://firebase.google.com/docs/admin/setup)
