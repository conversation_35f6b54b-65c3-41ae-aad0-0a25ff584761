#!/bin/bash

# Test Firebase Configuration Script
# This script helps test Firebase connectivity after deployment

set -e

# Configuration
SERVER_URL="${1:-http://www.lesidiotsduvillage.ch:8080}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO:${NC} $1"
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

log "Testing Firebase configuration on $SERVER_URL"

# Test basic health endpoint
log "Testing basic health endpoint..."
if curl -f -s "$SERVER_URL/api/health" > /tmp/health.json; then
    log "✅ Basic health endpoint is working"
    
    # Parse and display health status
    if command -v jq &> /dev/null; then
        echo "Health Status:"
        cat /tmp/health.json | jq '.'
    else
        echo "Health Response (install jq for pretty formatting):"
        cat /tmp/health.json
    fi
    echo
else
    error "❌ Basic health endpoint failed"
    exit 1
fi

# Test Firebase-specific health endpoint
log "Testing Firebase health endpoint..."
if curl -f -s "$SERVER_URL/api/health/firebase" > /tmp/firebase-health.json; then
    log "✅ Firebase health endpoint is working"
    
    # Parse and display Firebase status
    if command -v jq &> /dev/null; then
        echo "Firebase Status:"
        cat /tmp/firebase-health.json | jq '.'
        
        # Check if Firebase is properly initialized
        FIREBASE_INITIALIZED=$(cat /tmp/firebase-health.json | jq -r '.initialized // false')
        if [ "$FIREBASE_INITIALIZED" = "true" ]; then
            log "✅ Firebase is properly initialized"
        else
            warning "⚠️  Firebase is not initialized - check service account file"
        fi
        
    else
        echo "Firebase Response (install jq for pretty formatting):"
        cat /tmp/firebase-health.json
    fi
    echo
else
    error "❌ Firebase health endpoint failed"
    echo "This might indicate that the new version with Firebase support is not deployed yet."
fi

# Test actuator health endpoint
log "Testing Spring Boot Actuator health endpoint..."
if curl -f -s "$SERVER_URL/actuator/health" > /tmp/actuator-health.json; then
    log "✅ Actuator health endpoint is working"
    
    if command -v jq &> /dev/null; then
        echo "Actuator Health:"
        cat /tmp/actuator-health.json | jq '.'
    else
        echo "Actuator Response:"
        cat /tmp/actuator-health.json
    fi
    echo
else
    warning "⚠️  Actuator health endpoint not available (might not be deployed yet)"
fi

# Clean up temp files
rm -f /tmp/health.json /tmp/firebase-health.json /tmp/actuator-health.json

log "Firebase configuration test completed!"
echo
info "Next steps if Firebase is not working:"
echo "1. Ensure the Firebase service account file is at: /opt/botc-assistant/secrets/firebase-service-account.json"
echo "2. Check file permissions: sudo ls -la /opt/botc-assistant/secrets/"
echo "3. Verify environment variables: sudo cat /opt/botc-assistant/.env"
echo "4. Check Docker container logs: docker logs botc-assistant-container"
echo "5. Rebuild and redeploy with the new Firebase support"
