#!/bin/bash

# Setup Secrets Script for Blood on the Clocktower Assistant
# This script helps you securely configure environment variables and secrets

set -e

# Configuration
APP_DIR="/opt/botc-assistant"
ENV_FILE="$APP_DIR/.env"
SECRETS_DIR="$APP_DIR/secrets"
APP_USER="botcassistant"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO:${NC} $1"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    error "This script must be run as root (use sudo)"
    exit 1
fi

log "Setting up secrets and environment configuration..."

# Create directories
log "Creating application directories..."
mkdir -p "$APP_DIR"/{data,backups,logs,secrets}

# Set directory permissions
log "Setting directory permissions..."
chmod 755 "$APP_DIR"
chmod 755 "$APP_DIR"/{data,backups,logs}
chmod 700 "$SECRETS_DIR"  # Restrict access to secrets

# Change ownership
if id "$APP_USER" &>/dev/null; then
    chown -R "$APP_USER:$APP_USER" "$APP_DIR"
    log "Set ownership to $APP_USER"
else
    warning "User $APP_USER does not exist. Please create it first or run server-setup.sh"
fi

# Create environment file template if it doesn't exist
if [ ! -f "$ENV_FILE" ]; then
    log "Creating environment file template..."
    cat > "$ENV_FILE" << 'EOF'
# Blood on the Clocktower Assistant - Production Environment Configuration

# Server Configuration
SPRING_PROFILES_ACTIVE=production
SERVER_PORT=8080

# Firebase Admin SDK Configuration
FIREBASE_SERVICE_ACCOUNT_PATH=/app/secrets/firebase-service-account.json

# Database Configuration
SPRING_DATASOURCE_URL=*****************************
SPRING_JPA_HIBERNATE_DDL_AUTO=update

# Logging Configuration
LOGGING_LEVEL_ORG_SPRINGFRAMEWORK=INFO
LOGGING_LEVEL_COM_BOTC_ASSISTANT=DEBUG

# CORS Configuration (update with your domain)
# CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Health Check Configuration
MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE=health,info
MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS=when-authorized

# JVM Configuration
JAVA_OPTS=-XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0 -XX:+UseG1GC -XX:+UseStringDeduplication
EOF

    chmod 600 "$ENV_FILE"
    if id "$APP_USER" &>/dev/null; then
        chown "$APP_USER:$APP_USER" "$ENV_FILE"
    fi
    
    log "Environment file created at $ENV_FILE"
    info "Please edit $ENV_FILE to configure your specific settings"
else
    log "Environment file already exists at $ENV_FILE"
fi

# Instructions for Firebase service account
echo
info "=== Firebase Service Account Setup ==="
echo "To set up your Firebase service account:"
echo "1. Download your Firebase service account JSON file from the Firebase Console"
echo "2. Copy it to the server using one of these methods:"
echo
echo "   Method 1 - SCP (from your local machine):"
echo "   scp firebase-service-account.json user@yourserver:/tmp/"
echo "   sudo mv /tmp/firebase-service-account.json $SECRETS_DIR/"
echo
echo "   Method 2 - Direct copy (if file is already on server):"
echo "   sudo cp /path/to/firebase-service-account.json $SECRETS_DIR/"
echo
echo "3. Set proper permissions:"
echo "   sudo chmod 600 $SECRETS_DIR/firebase-service-account.json"
if id "$APP_USER" &>/dev/null; then
    echo "   sudo chown $APP_USER:$APP_USER $SECRETS_DIR/firebase-service-account.json"
fi
echo

# Check if Firebase service account exists
if [ -f "$SECRETS_DIR/firebase-service-account.json" ]; then
    log "Firebase service account file found"
    chmod 600 "$SECRETS_DIR/firebase-service-account.json"
    if id "$APP_USER" &>/dev/null; then
        chown "$APP_USER:$APP_USER" "$SECRETS_DIR/firebase-service-account.json"
    fi
    log "Firebase service account permissions updated"
else
    warning "Firebase service account file not found at $SECRETS_DIR/firebase-service-account.json"
    info "Please follow the instructions above to set it up"
fi

# Display current setup
echo
log "=== Current Setup ==="
echo "App Directory: $APP_DIR"
echo "Environment File: $ENV_FILE"
echo "Secrets Directory: $SECRETS_DIR"
echo
echo "Directory Permissions:"
ls -la "$APP_DIR"
echo
echo "Secrets Directory:"
ls -la "$SECRETS_DIR" 2>/dev/null || echo "Secrets directory is empty or inaccessible"
echo

log "Setup completed!"
info "Next steps:"
echo "1. Edit $ENV_FILE with your configuration"
echo "2. Copy your Firebase service account JSON file to $SECRETS_DIR/"
echo "3. Run your deployment: ./deploy/deploy.sh your-image:tag"
