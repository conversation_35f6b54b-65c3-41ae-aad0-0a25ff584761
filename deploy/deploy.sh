#!/bin/bash

# Blood on the Clocktower Assistant Deployment Script
# This script handles zero-downtime deployment with health checks and rollback

set -e

# Configuration
APP_NAME="botc-assistant"
CONTAINER_NAME="${APP_NAME}-container"
NETWORK_NAME="${APP_NAME}-network"
DATA_DIR="/opt/botc-assistant/data"
BACKUP_DIR="/opt/botc-assistant/backups"
LOG_FILE="/opt/botc-assistant/logs/deploy.log"
ENV_FILE="/opt/botc-assistant/.env"
SECRETS_DIR="/opt/botc-assistant/secrets"
HEALTH_CHECK_URL="http://localhost:8080/"
MAX_HEALTH_CHECKS=30
HEALTH_CHECK_INTERVAL=2

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function with fallback
log() {
    local msg="${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
    echo -e "$msg"
    echo -e "$msg" >> "$LOG_FILE" 2>/dev/null || true
}

error() {
    local msg="${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
    echo -e "$msg"
    echo -e "$msg" >> "$LOG_FILE" 2>/dev/null || true
}

warning() {
    local msg="${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
    echo -e "$msg"
    echo -e "$msg" >> "$LOG_FILE" 2>/dev/null || true
}

# Check if image parameter is provided
if [ -z "$1" ]; then
    error "Usage: $0 <docker-image>"
    exit 1
fi

NEW_IMAGE="$1"

log "Starting deployment of $NEW_IMAGE"
log "Image name validation: checking if image name is lowercase..."

# Validate image name format
if [[ "$NEW_IMAGE" =~ [A-Z] ]]; then
    warning "Image name contains uppercase characters: $NEW_IMAGE"
    log "Converting to lowercase..."
    NEW_IMAGE=$(echo "$NEW_IMAGE" | tr '[:upper:]' '[:lower:]')
    log "Converted image name: $NEW_IMAGE"
fi

# Create necessary directories with proper permissions
mkdir -p "$DATA_DIR" "$BACKUP_DIR" "$(dirname "$LOG_FILE")" "$SECRETS_DIR"
chmod 755 "$DATA_DIR" "$BACKUP_DIR" "$(dirname "$LOG_FILE")"
chmod 700 "$SECRETS_DIR"  # Restrict access to secrets directory

# Ensure log file exists and is writable
touch "$LOG_FILE"
chmod 644 "$LOG_FILE"

# Create Docker network if it doesn't exist
if ! docker network ls | grep -q "$NETWORK_NAME"; then
    log "Creating Docker network: $NETWORK_NAME"
    docker network create "$NETWORK_NAME"
fi

# No database backup needed for static landing page

# Get current container ID if running
CURRENT_CONTAINER=$(docker ps -q -f name="$CONTAINER_NAME" 2>/dev/null || true)

# Pull the new image
log "Pulling new image: $NEW_IMAGE"
if ! docker pull "$NEW_IMAGE"; then
    error "Failed to pull image: $NEW_IMAGE"
    exit 1
fi

# Start new container with temporary name
TEMP_CONTAINER="${CONTAINER_NAME}-new"
log "Starting new container: $TEMP_CONTAINER"

# Prepare environment variables and secrets
ENV_ARGS=""
if [ -f "$ENV_FILE" ]; then
    ENV_ARGS="--env-file $ENV_FILE"
    log "Using environment file: $ENV_FILE"
fi

VOLUME_ARGS="-v $DATA_DIR:/app/data"
if [ -f "$SECRETS_DIR/firebase-service-account.json" ]; then
    VOLUME_ARGS="$VOLUME_ARGS -v $SECRETS_DIR/firebase-service-account.json:/app/secrets/firebase-service-account.json:ro"
    log "Mounting Firebase service account file"
fi

docker run -d \
    --name "$TEMP_CONTAINER" \
    --network "$NETWORK_NAME" \
    -p 8081:8080 \
    $ENV_ARGS \
    $VOLUME_ARGS \
    -e SPRING_PROFILES_ACTIVE=production \
    --restart unless-stopped \
    "$NEW_IMAGE"

# Health check function
health_check() {
    local port=$1
    local url="http://localhost:${port}/"
    
    for i in $(seq 1 $MAX_HEALTH_CHECKS); do
        log "Health check attempt $i/$MAX_HEALTH_CHECKS"
        
        if curl -f -s "$url" > /dev/null 2>&1; then
            log "Health check passed!"
            return 0
        fi
        
        if [ $i -lt $MAX_HEALTH_CHECKS ]; then
            sleep $HEALTH_CHECK_INTERVAL
        fi
    done
    
    error "Health check failed after $MAX_HEALTH_CHECKS attempts"
    return 1
}

# Wait for new container to be healthy
log "Performing health checks on new container..."
if ! health_check 8081; then
    error "New container failed health checks, rolling back..."
    docker stop "$TEMP_CONTAINER" 2>/dev/null || true
    docker rm "$TEMP_CONTAINER" 2>/dev/null || true
    exit 1
fi

# Switch traffic to new container
log "Switching traffic to new container..."

# Stop old container if it exists
if [ -n "$CURRENT_CONTAINER" ]; then
    log "Stopping old container: $CURRENT_CONTAINER"
    docker stop "$CURRENT_CONTAINER" 2>/dev/null || true
fi

# Remove port mapping from new container and add correct one
log "Reconfiguring container networking..."
docker stop "$TEMP_CONTAINER"

# Remove old container
if [ -n "$CURRENT_CONTAINER" ]; then
    docker rm "$CURRENT_CONTAINER" 2>/dev/null || true
fi

# Rename temp container to final name and restart with correct port
docker rename "$TEMP_CONTAINER" "$CONTAINER_NAME"

# Start with correct port mapping
docker run -d \
    --name "${CONTAINER_NAME}-final" \
    --network "$NETWORK_NAME" \
    -p 8080:8080 \
    $ENV_ARGS \
    $VOLUME_ARGS \
    -e SPRING_PROFILES_ACTIVE=production \
    --restart unless-stopped \
    "$NEW_IMAGE"

# Remove the renamed container
docker rm "$CONTAINER_NAME" 2>/dev/null || true

# Rename final container
docker rename "${CONTAINER_NAME}-final" "$CONTAINER_NAME"

# Final health check
log "Performing final health check..."
if ! health_check 8080; then
    error "Final health check failed! Manual intervention required."
    exit 1
fi

# Cleanup old images (keep last 3)
log "Cleaning up old images..."
docker images --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}\t{{.ID}}" | \
    grep "$(echo "$NEW_IMAGE" | cut -d: -f1)" | \
    tail -n +4 | \
    awk '{print $3}' | \
    xargs -r docker rmi 2>/dev/null || true

log "Deployment completed successfully!"
log "Application is running at http://localhost:8080"
